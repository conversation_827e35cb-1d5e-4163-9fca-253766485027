{"server": {"port": 31337, "plugin": {"device-farm": {"platform": "android", "remoteMachineProxyIP": "http://10.x.x.x:3333", "skipChromeDownload": true, "androidDeviceType": "real", "iosDeviceType": "simulated", "wdaBundleId": "atd.pro", "preBuildWDAPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WebDriverAgent-egfeypzyldpeeqdwikoxqbnopkla/Build/Products/Debug-iphonesimulator/WebDriverAgentRunner-Runner.app", "adbRemote": ["***********:5037", "***********:5037"], "hub": "https://hubip:port", "maxSessions": 2, "emulators": [{"avdName": "Pixel_5_API_33", "launchTimeout": 120000}, {"avdName": "nightwatch-android-11", "launchTimeout": 10000}], "derivedDataPath": {"simulator": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WebDriverAgent-Test", "device": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WebDriverAgent-Test"}, "deviceAvailabilityTimeoutMs": 300000, "deviceAvailabilityQueryIntervalMs": 10000, "sendNodeDevicesToHubIntervalMs": 30000, "checkStaleDevicesIntervalMs": 30000, "checkBlockedDevicesIntervalMs": 30000, "newCommandTimeoutSec": 60, "bindHostOrIp": "*********", "bootedSimulators": true}}}}