## [10.0.6](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v10.0.5...v10.0.6) (2025-07-15)

### Bug Fixes

* **getDeviceTypeFromApp:** strip query params and properly detect .app/.zip/.ipa builds ([#1738](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1738)) ([5c20f36](https://github.com/AppiumTestDistribution/appium-device-farm/commit/5c20f36ff1401d6bd1387ba022696d91327f4894))

## [10.0.5](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v10.0.4...v10.0.5) (2025-06-15)

### Bug Fixes

* update IOSDeviceType.ts for iPhone 16e ([#1716](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1716)) ([2416557](https://github.com/AppiumTestDistribution/appium-device-farm/commit/24165570f6a349cc083d03b558d2390f43df8e46))

## [10.0.4](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v10.0.3...v10.0.4) (2025-05-19)

### Bug Fixes

* remove node-pty dependency ([#1692](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1692)) ([32ca76a](https://github.com/AppiumTestDistribution/appium-device-farm/commit/32ca76aae8f399fc626962fe130ee3929293ac33))

## [10.0.3](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v10.0.2...v10.0.3) (2025-05-12)

### Bug Fixes

* ignore user check while session creation when auth is disabled ([#1676](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1676)) ([6b4a38b](https://github.com/AppiumTestDistribution/appium-device-farm/commit/6b4a38b944f3880e9d06ca76a88aa60b179f597d))

## [10.0.2](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v10.0.1...v10.0.2) (2025-05-12)

### Bug Fixes

* display active user on device card ([#1675](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1675)) ([89f0eb2](https://github.com/AppiumTestDistribution/appium-device-farm/commit/89f0eb2a6c1cd917cc12a56f21bd298d5c313353))

## [10.0.1](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v10.0.0...v10.0.1) (2025-05-09)

### Bug Fixes

* update documentations for authentication module ([#1673](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1673)) ([4e35bed](https://github.com/AppiumTestDistribution/appium-device-farm/commit/4e35bed81e007032882f48dde402c705b27e2180))

## [10.0.0](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.8...v10.0.0) (2025-05-09)

### ⚠ BREAKING CHANGES

* Following changes are added
- Added authentication module with user and team management
- Enhanced automation runs with access key authentication
- Improved device and server management capabilities
- Database schema changes that may result in loss of existing data

### Features

* use newCommandTimeoutSec for manual device control ([#1672](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1672)) ([7149941](https://github.com/AppiumTestDistribution/appium-device-farm/commit/71499417b2ef9c6229b8b493a0984a0ddff26799))

## [9.8.8](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.7...v9.8.8) (2025-04-29)

### Bug Fixes

* android streaming and double click issue ([#1663](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1663)) ([a3c88a0](https://github.com/AppiumTestDistribution/appium-device-farm/commit/a3c88a0e55ffe588257f2ecccd2e391726870c39))

## [9.8.7](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.6...v9.8.7) (2025-04-22)

### Bug Fixes

* iOS resigning script ([#1655](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1655)) ([3f565b4](https://github.com/AppiumTestDistribution/appium-device-farm/commit/3f565b450a5b1271d91c30aace9d697e5c585aca))

## [9.8.6](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.5...v9.8.6) (2025-04-21)

### Bug Fixes

* Running device-farm in non mac ([#1653](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1653)) ([8e7beca](https://github.com/AppiumTestDistribution/appium-device-farm/commit/8e7becae3047fbb161935ca294beb795e5f759a3))

## [9.8.5](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.4...v9.8.5) (2025-03-31)

### Bug Fixes

* address open issues and update base plugin versions ([#1631](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1631)) ([19b0cd1](https://github.com/AppiumTestDistribution/appium-device-farm/commit/19b0cd1b40fb61f2550dbe00e9f9d25d4c41ed50))

## [9.8.4](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.3...v9.8.4) (2025-03-18)

### Bug Fixes

* add support for file browser and app management for android ([#1597](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1597)) ([4aed0d5](https://github.com/AppiumTestDistribution/appium-device-farm/commit/4aed0d5defc2628a72af5fb994e06b61baf5ffee))

## [9.8.3](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.2...v9.8.3) (2025-03-05)

### Bug Fixes

* display realtime adb logs during manual device control ([#1581](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1581)) ([70d6efa](https://github.com/AppiumTestDistribution/appium-device-farm/commit/70d6efa54bbc040043f6a1a05fc07fdfbb3b85c0))

## [9.8.2](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.1...v9.8.2) (2025-02-24)

### Bug Fixes

* installation issue due to incorrect config loading ([#1578](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1578)) ([f4eca35](https://github.com/AppiumTestDistribution/appium-device-farm/commit/f4eca35dc80bd79bef2deea93204a0d90d51e094))

## [9.8.1](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.8.0...v9.8.1) (2025-02-23)

### Bug Fixes

* make debugging simple by serving appium logs in the dashboard ([#1576](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1576)) ([c787f97](https://github.com/AppiumTestDistribution/appium-device-farm/commit/c787f97ed316b69461c1bfefeeb17610a1920944))

## [9.8.0](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.7.2...v9.8.0) (2025-02-22)

### Features

* making the UI great again  ([#1560](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1560)) ([815554f](https://github.com/AppiumTestDistribution/appium-device-farm/commit/815554f491d986f8e256012553683eb999f3e420))

## [9.7.2](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.7.1...v9.7.2) (2025-02-21)

### Bug Fixes

* build error ([#1568](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1568)) ([0720ab8](https://github.com/AppiumTestDistribution/appium-device-farm/commit/0720ab8bbd8a00d26fd64fc3ab45470be9325b63))

## [9.7.1](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.7.0...v9.7.1) (2025-02-21)

### Bug Fixes

* useDevice card issue on browser reload ([#1567](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1567)) ([d37779b](https://github.com/AppiumTestDistribution/appium-device-farm/commit/d37779bd3c67dcdfb5d4c91638a83f81758b7f65))

## [9.7.0](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.6.0...v9.7.0) (2025-02-21)

### Features

* 1459 add support to edit device name in the dashboard ([#1564](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1564)) ([9771ddb](https://github.com/AppiumTestDistribution/appium-device-farm/commit/9771ddbafdcbbff3ac6555ae0535b2a1353690b4))

## [9.6.0](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.5.4...v9.6.0) (2025-02-20)

### Features

* add ability to customize stream parameters ([#1556](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1556)) ([5d2780a](https://github.com/AppiumTestDistribution/appium-device-farm/commit/5d2780a067a3ad348b076fdba68959ee50d2ae45))

### Bug Fixes

* ts build errors ([#1557](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1557)) ([93747bc](https://github.com/AppiumTestDistribution/appium-device-farm/commit/93747bc91bbf0e2992dba5c8217200c88280c834))

## [9.5.4](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.5.3...v9.5.4) (2025-02-18)

### Bug Fixes

* handle proper mjpegServerPort for ios streaming from node ([#1550](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1550)) ([07bd279](https://github.com/AppiumTestDistribution/appium-device-farm/commit/07bd279af1492e8f2ebab35ef5f067bd92b5c833))

## [9.5.3](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.5.2...v9.5.3) (2025-02-18)

### Bug Fixes

* update screen size for iPad Pro 13-inch ([#1549](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1549)) ([f46418f](https://github.com/AppiumTestDistribution/appium-device-farm/commit/f46418f3189dedc53964b660743472c8d585b159))

## [9.5.2](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.5.1...v9.5.2) (2025-02-17)

### Bug Fixes

* ios port forwarding for real device ([#1539](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1539)) ([c4b8ef0](https://github.com/AppiumTestDistribution/appium-device-farm/commit/c4b8ef0186816b2c805fbb2ef9778c84b617ccc3))

## [9.5.1](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.5.0...v9.5.1) (2025-02-17)

### Bug Fixes

* revert ios streaming from ws to http ([#1533](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1533)) ([6ed8641](https://github.com/AppiumTestDistribution/appium-device-farm/commit/6ed86411cdbcbd3fe3d8819ea209a7d91e89385c))
* update publish yaml for submodule checkout ([#1535](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1535)) ([4d60fbf](https://github.com/AppiumTestDistribution/appium-device-farm/commit/4d60fbf2c5aa5a5aa52cf8acea0fddd4c011cb64))
* update Width and Height for iPad Mini 7 ([#1522](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1522)) ([2998a89](https://github.com/AppiumTestDistribution/appium-device-farm/commit/2998a89dbe4b5f591f2780a9ef273d7457ad9cd5))

## [9.5.0](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.17...v9.5.0) (2025-01-16)

### Features

* improve device streaming and support for remote network ([#1511](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1511)) ([ff01caa](https://github.com/AppiumTestDistribution/appium-device-farm/commit/ff01caa6b5a59ede00dcd0f6048260a795bc12e1))

## [9.4.17](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.16...v9.4.17) (2025-01-03)

### Miscellaneous Chores

* add workflow for main and PR ([#1498](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1498)) ([9524846](https://github.com/AppiumTestDistribution/appium-device-farm/commit/952484643f744117a602f561af857b8a7ffb7d48))

## [9.4.16](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.15...v9.4.16) (2024-12-27)

### Bug Fixes

* Update IOSDeviceType.ts ([#1488](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1488)) ([55165df](https://github.com/AppiumTestDistribution/appium-device-farm/commit/55165df913a3121f74c2df40274e38e8494ecdd6))
* Update IOSDeviceType.ts ([#1492](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1492)) ([b5f13f5](https://github.com/AppiumTestDistribution/appium-device-farm/commit/b5f13f5b13cf84b3c9506150dd168fb8a7718989))

## [9.4.15](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.14...v9.4.15) (2024-12-20)

### Bug Fixes

* added missing nodeId for ios simulators when registered from node ([#1487](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1487)) ([b4f6a0c](https://github.com/AppiumTestDistribution/appium-device-farm/commit/b4f6a0c5a959a7560353dbb44fc084b9bedfec85))

## [9.4.14](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.13...v9.4.14) (2024-12-17)

### Bug Fixes

* LT device allocation ([#1482](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1482)) ([357c094](https://github.com/AppiumTestDistribution/appium-device-farm/commit/357c094aac8cc793cc0c7a33fd53bb169f589645))

## [9.4.13](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.12...v9.4.13) (2024-12-16)

### Bug Fixes

* add logic to ignore websocket connectivity ([#1477](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1477)) ([47c403b](https://github.com/AppiumTestDistribution/appium-device-farm/commit/47c403b629d607da273d5cba3365b9eb4d4631cd))

## [9.4.12](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.11...v9.4.12) (2024-12-15)

### Bug Fixes

* handle permission errors dutring adb list package ([#1473](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1473)) ([5db19fa](https://github.com/AppiumTestDistribution/appium-device-farm/commit/5db19fa9f0a6dab3844bdc784e5c820a56ef8170))

## [9.4.11](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.10...v9.4.11) (2024-12-13)

### Bug Fixes

* provisioning profile file path after Xcode 16 ([#1471](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1471)) ([3114a4d](https://github.com/AppiumTestDistribution/appium-device-farm/commit/3114a4d566bd871ce0bb2299376726b729fa21ec))

## [9.4.10](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.9...v9.4.10) (2024-12-12)

### Bug Fixes

* imporve port management for ios parallel execution ([#1470](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1470)) ([a6992b5](https://github.com/AppiumTestDistribution/appium-device-farm/commit/a6992b5caa30aa09141ed9d0f5ef5d7b4301f9c1))

## [9.4.9](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.8...v9.4.9) (2024-12-12)

### Bug Fixes

* Unable to take ios devices from node for manual interaction ([#1469](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1469)) ([9c079e8](https://github.com/AppiumTestDistribution/appium-device-farm/commit/9c079e8babc3b6ef20e2adae3394f72aeb40dc97))

## [9.4.8](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.7...v9.4.8) (2024-12-11)

### Bug Fixes

* streamig issues ([#1467](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1467)) ([4dd563a](https://github.com/AppiumTestDistribution/appium-device-farm/commit/4dd563a507662c0bf4ff5b2e53df5b065d6d9d89))

## [9.4.7](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.6...v9.4.7) (2024-12-11)

### Bug Fixes

* Fix/lambdatest ([#1468](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1468)) ([33aae24](https://github.com/AppiumTestDistribution/appium-device-farm/commit/33aae24ea38d954069bb61960b7bb8ee9ada5dce))

## [9.4.6](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.5...v9.4.6) (2024-11-18)

### Bug Fixes

* force npm publish ([#1449](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1449)) ([c390b42](https://github.com/AppiumTestDistribution/appium-device-farm/commit/c390b42de113e312fb030e8b7e461c5509e8ae96))

## [9.4.5](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.4...v9.4.5) (2024-10-25)

### Bug Fixes

* use dynamic ports for wda ([#1431](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1431)) ([bf390bd](https://github.com/AppiumTestDistribution/appium-device-farm/commit/bf390bdbc6eddb1ed9bf4fe2108064ecbe1e662e))

## [9.4.4](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.3...v9.4.4) (2024-10-25)

### Bug Fixes

* enable livestream for android based on capability ([#1430](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1430)) ([46d2a04](https://github.com/AppiumTestDistribution/appium-device-farm/commit/46d2a0455f20159fb20009e73c75b5795fed0ecd))

## [9.4.3](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.2...v9.4.3) (2024-10-24)

### Bug Fixes

* improve device allocation and session creation logic ([#1427](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1427)) ([4405f79](https://github.com/AppiumTestDistribution/appium-device-farm/commit/4405f79e9f47bfea32531310385f545b376911a8))

## [9.4.2](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.1...v9.4.2) (2024-10-24)

### Bug Fixes

* bump version ([#1426](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1426)) ([78613c7](https://github.com/AppiumTestDistribution/appium-device-farm/commit/78613c7e1f584fce769978b1e96c13501b223ac4))

## [9.4.1](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.4.0...v9.4.1) (2024-10-04)

### Bug Fixes

* release bump ([#1409](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1409)) ([c9bfea8](https://github.com/AppiumTestDistribution/appium-device-farm/commit/c9bfea862f4fc7ae65e15953a49beec5609d5ea2))

## [9.4.0](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.3.0...v9.4.0) (2024-10-02)

### Features

* add support for paste and arrow keys ([#1404](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1404)) ([1404592](https://github.com/AppiumTestDistribution/appium-device-farm/commit/1404592714d359c4eb2f5bcb116d0860698fc0eb))

## [9.3.0](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.11...v9.3.0) (2024-09-29)

### Features

* add support for keyboard interaction ([#1402](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1402)) ([5846b92](https://github.com/AppiumTestDistribution/appium-device-farm/commit/5846b924b4a37521db52f72197dc681d77ed727a))

## [9.2.11](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.10...v9.2.11) (2024-09-02)

### Bug Fixes

* add support to flutter integration driver ([#1355](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1355)) ([683c61c](https://github.com/AppiumTestDistribution/appium-device-farm/commit/683c61c1ee5400cf12ab2f40de3450803fa01f05))

## [9.2.10](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.9...v9.2.10) (2024-08-27)

### Bug Fixes

* add wdalocalport when running parallel sessions ([#1342](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1342)) ([1f7144b](https://github.com/AppiumTestDistribution/appium-device-farm/commit/1f7144bd77c938b01fec1b32a75080a4ad432c26))

## [9.2.9](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.8...v9.2.9) (2024-08-26)

### Bug Fixes

* WDA IPA file and update submodule ([#1328](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1328)) ([2295434](https://github.com/AppiumTestDistribution/appium-device-farm/commit/229543456f1edc744d29d317f3d27e5126f416d6))

## [9.2.8](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.7...v9.2.8) (2024-08-24)

### Bug Fixes

* error handling and ios signing script ([#1306](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1306)) ([0d9f206](https://github.com/AppiumTestDistribution/appium-device-farm/commit/0d9f20661844c6f92d35d4203ebc4ea01d59b540))

## [9.2.7](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.6...v9.2.7) (2024-08-22)

### Miscellaneous Chores

* **deps:** update dependency chai-exclude to v2.1.1 ([#1203](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1203)) ([740a604](https://github.com/AppiumTestDistribution/appium-device-farm/commit/740a604dd93e1d2eb48f3698490f0b833a9aa8af))

## [9.2.6](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.5...v9.2.6) (2024-08-22)

### Miscellaneous Chores

* **deps:** update dependency chai-as-promised to v7.1.2 ([faf9016](https://github.com/AppiumTestDistribution/appium-device-farm/commit/faf9016b3e23fb2515bb268be81e916aeff852e2))

## [9.2.5](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.4...v9.2.5) (2024-08-22)

### Miscellaneous Chores

* **deps:** update dependency @appium/plugin-test-support to v0.3.41 ([8ea1918](https://github.com/AppiumTestDistribution/appium-device-farm/commit/8ea1918114db978bbdbf4111a86bb2d0c4796e99))

## [9.2.4](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.3...v9.2.4) (2024-08-22)

### Bug Fixes

* Fix running cloud test from linux machine ([#1297](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1297)) ([b7bb070](https://github.com/AppiumTestDistribution/appium-device-farm/commit/b7bb07066c9b8341700e9c608398045a2a37a915))

## [9.2.3](https://github.com/AppiumTestDistribution/appium-device-farm/compare/v9.2.2...v9.2.3) (2024-08-19)

### Bug Fixes

* improvements to error handling and webpack build ([#1287](https://github.com/AppiumTestDistribution/appium-device-farm/issues/1287)) ([e84af6f](https://github.com/AppiumTestDistribution/appium-device-farm/commit/e84af6feba9fc7892d6ddb438030bcd17c13af8a))
