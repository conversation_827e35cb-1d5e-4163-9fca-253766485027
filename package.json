{"name": "appium-device-farm", "version": "10.0.6", "description": "An appium 2.0 plugin that manages and create driver session on available devices.", "main": "./lib/src/main.js", "scripts": {"build:docs": "appium-docs build --reference=false", "publish:docs": "appium-docs build --deploy --push -b docs-site -m 'docs: auto-build docs for appium-device-farm@%s' --alias latest", "install-docs-deps": "appium-docs init --no-mkdocs -e lib/src/index.js", "coverage": "nyc npm run test", "test-remote": "mocha --require ts-node/register ./test/e2e/remote.spec.js --timeout 60000 --exit", "test": "mocha -r ts-node/register ./test/unit/*.spec.{j,t}s --plugin-device-farm-platform=both --exit --timeout=20000", "test-jest": "NODE_OPTIONS=--experimental-vm-modules npx jest ./test/unit/AndroidDeviceManager.spec.js", "test-parallel-android": "mocha --require ts-node/register -p ./test/e2e/android/conf1.spec.js --exit --timeout 260000", "test-parallel-ios": "mocha --require ts-node/register -p ./test/e2e/ios/conf1.spec.js --exit --timeout 260000", "test-parallel-bs": "mocha --require ts-node/register -p ./test/e2e/android/cloud/bs.spec.ts --exit --timeout 260000", "test-parallel-pcloudy": "mocha --require ts-node/register -p ./test/e2e/android/cloud/pcloudy.spec.ts --exit --timeout 260000", "test-parallel-sauce": "wait-on http://localhost:31337/device-farm/ && mocha --require ts-node/register -p ./test/e2e/android/cloud/sauce.spec.js --exit --timeout 260000", "test-parallel-lt": "mocha --require ts-node/register -p ./test/e2e/android/cloud/lambdaTest.spec.ts --exit --timeout 260000", "test-parallel-ios-bs": "mocha --require ts-node/register -p ./test/e2e/android/cloud/confIOS.ts --exit --timeout 260000", "test-e2e": "mocha --require ts-node/register ./test/e2e/plugin.spec.* --exit --timeout=999999", "test-e2e-hubnode": "mocha -r ts-node/register ./test/e2e/hubnode/*.spec.ts --plugin-device-farm-platform=both --exit --timeout=999999", "test-e2e-browserstack": "mocha --require ts-node/register ./test/e2e/browserstack.spec.ts --exit --timeout 999999", "test-e2e-pcloudy": "mocha --require ts-node/register ./test/e2e/pcloudy.spec.ts --exit --timeout 999999", "integration-android": "mocha -r ts-node/register ./test/integration/androidDevices.spec.ts --timeout 90000 --exit", "integration-ios": "mocha -r ts-node/register ./test/integration/ios/*.spec.ts --timeout 260000 --exit", "integration-ios2": "mocha -r ts-node/register ./test/integration/ios/02iOSDevices.spec.ts --timeout 260000 --exit", "build": "rm -rf lib && npx tsc -b && npm run buildAndCopyWeb && npm run copy-files", "bundle": "npm run build && webpack", "build-web-and-plugin": "npx tsc -b && npm run buildAndCopyWeb && npm run copy-files", "copy-files": "(cp -R src/public lib || exit 0)", "buildAndCopyWeb": "sh buildAndCopyWeb.sh", "bundleStreamApk": "sh move-stream.sh", "prepublishOnly": "npx tsc && npm run buildAndCopyWeb && npm run copy-files && npm run bundle", "lint": "eslint . --ext .ts,.tsx --fix", "prettier-check": "prettier 'src/**/*.ts' --check --verbose", "prettier": "prettier 'src/**/*.ts' 'test/**/*.ts' --write --single-quote", "appium-home": "rm -rf /tmp/some-temp-dir && export APPIUM_HOME=/tmp/some-temp-dir", "install-plugin": "npm run buildAndCopyWeb && npm run bundle && appium plugin install --source=local $(pwd)", "clear-cache": "rm -rf $HOME/.cache/appium-device-farm", "install-driver": "export APPIUM_HOME=/tmp/some-temp-dir && appium driver install uiautomator2", "reinstall-plugin": "export APPIUM_HOME=/tmp/some-temp-dir && npm run appium-home && (appium plugin uninstall device-farm || exit 0) && npm run install-plugin", "run-server": "export APPIUM_HOME=/tmp/some-temp-dir && appium server -ka 800 --use-plugins=device-farm -pa /wd/hub --plugin-device-farm-platform=android --plugin-device-farm-max-sessions=8", "run-db-migration": "path-exists lib/src/scripts/initialize-database.js && node lib/src/scripts/initialize-database.js || ts-node src/scripts/initialize-database.ts", "generate-migration": "ts-node src/scripts/generate-database-migration.ts", "postinstall": "npm run run-db-migration", "proxy": "lcp --proxyUrl http://127.0.0.1:4723 --port 4444"}, "repository": {"type": "git", "url": "https://github.com/AppiumTestDistribution/appium-device-farm"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "sa<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "ISC", "bugs": {"url": "https://github.com/AppiumTestDistribution/appium-device-farm/issues"}, "homepage": "https://github.com/AppiumTestDistribution/appium-device-farm#readme", "dependencies": {"@appium/base-plugin": "^2.3.5", "@appium/types": "^0.x", "@devicefarmer/adbkit": "^3.2.6", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@prisma/client": "^5.15.0", "@types/appium-adb": "^9.10.4", "@types/archiver": "^6.0.3", "@types/node-persist": "^3.1.5", "appium-adb": "^11.0.3", "appium-chromedriver": "^5.6.19", "appium-ios-device": "^2.7.13", "applesign": "^5.0.0", "archiver": "^7.0.1", "arraybuffer-equal": "^1.0.4", "async-lock": "^1.2.8", "async-wait-until": "^2.0.12", "asyncbox": "^3.0.0", "axios": "^1.6.7", "bcrypt": "^5.1.1", "bluebird": "^3.7.2", "cors": "^2.8.5", "crypto": "^1.0.1", "emittery": "0.13.1", "eventemitter2": "^6.4.9", "express": "^4.17.3", "form-data": "^4.0.0", "fs-extra": "^11.1.1", "generate-api-key": "^1.0.2", "get-port": "^5.1.1", "http-proxy-agent": "7.0.2", "http-proxy-middleware": "^3.0.0-beta.1", "https-proxy-agent": "7.0.5", "inquirer": "^10.1.8", "ios-mobileprovision-finder": "^1.1.0", "ip": "^1.1.8", "jsonschema": "1.4.1", "jsonwebtoken": "^9.0.2", "listr": "^0.14.3", "local-cors-proxy": "^1.1.0", "lodash": "4.17.21", "lokijs": "^1.5.12", "lru-cache": "^10.2.2", "mjpeg-proxy": "^0.3.0", "multer": "^1.4.5-lts.1", "node-abort-controller": "^3.1.1", "node-persist": "^3.1.3", "node-simctl": "^7.3.13", "normalize-url": "6.1.0", "nyc": "^15.1.0", "ora": "5.4.1", "path-exists-cli": "^2.0.0", "plist": "^3.1.0", "portscanner": "^2.2.0", "prisma": "^5.15.0", "q": "^1.5.1", "ramda": "^0.30.0", "reflect-metadata": "^0.2.0", "rxjs": "^6.6.7", "semver": "^7.5.4", "sharp": "^0.33.5", "tcp-port-used": "^1.0.2", "typedi": "^0.10.0", "uuid": "^8.3.2", "uuid-by-string": "^4.0.0", "ws": "^8.17.0", "yargs": "^17.7.2"}, "peerDependencies": {"appium": "^2.11.3"}, "devDependencies": {"@appium/docutils": "^0.4.11", "@appium/fake-driver": "^5.2.10", "@appium/plugin-test-support": "^0.3.23", "@babel/core": "7.25.2", "@babel/plugin-proposal-class-properties": "7.18.6", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.1", "@types/async-lock": "1.4.2", "@types/bcrypt": "^5.0.2", "@types/bluebird": "^3.5.42", "@types/chai": "4.3.19", "@types/chai-as-promised": "^7.1.8", "@types/express": "4.17.21", "@types/fs-extra": "^11.0.0", "@types/inquirer": "^9.0.7", "@types/ip": "^1.1.2", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.9", "@types/listr": "^0.14.9", "@types/lodash": "4.17.7", "@types/lokijs": "1.5.14", "@types/mocha": "9.1.1", "@types/multer": "^1.4.11", "@types/plist": "^3.0.5", "@types/portscanner": "^2.1.4", "@types/sinon": "^17.0.2", "@types/sinon-chai": "^3.2.12", "@types/tcp-port-used": "1.0.4", "@types/uuid": "8.3.4", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@wdio/types": "^8.24.2", "appium": "^2.17.1", "appium-flutter-integration-driver": "^1.1.3", "appium-uiautomator2-driver": "^4.2.3", "appium-xcuitest-driver": "^9.2.1", "babel-eslint": "10.1.0", "chai": "4.5.0", "chai-as-promised": "^7.1.1", "chai-exclude": "^2.1.0", "conventional-changelog-conventionalcommits": "^8.0.0", "dotenv": "^16.3.1", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.30.0", "eslint-plugin-prettier": "^5.0.0", "husky": "7.0.4", "javascript-obfuscator": "^4.1.0", "js-yaml": "^4.1.0", "lint-staged": "15.2.10", "mocha": "9.2.2", "prettier": "^3.0.3", "ramda": "0.30.1", "remove-files-webpack-plugin": "^1.5.0", "sinon": "17.0.1", "sinon-chai": "^3.7.0", "ts-node": "10.9.2", "typescript": "^5.5.4", "wait-on": "^7.2.0", "webdriverio": "^8.20.4", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0", "webpack-obfuscator": "^3.5.1"}, "appium": {"pluginName": "device-farm", "mainClass": "DevicePlugin", "schema": {"$schema": "http://json-schema.org/draft-07/schema", "additionalProperties": false, "description": "Appium configuration schema for the Device Farm plugin.", "properties": {"platform": {"type": "string"}, "androidDeviceType": {"type": "string", "default": "both"}, "simulators": {"type": "array"}, "iosDeviceType": {"type": "string", "default": "both"}, "hub": {"type": "string"}, "remoteMachineProxyIP": {"type": "string"}, "adbRemote": {"type": "array"}, "skipChromeDownload": {"type": "boolean"}, "maxSessions": {"type": "number"}, "cloud": {"type": "object"}, "derivedDataPath": {"type": "object"}, "emulators": {"type": "array"}, "proxy": {"type": "object"}, "deviceAvailabilityTimeoutMs": {"type": "number"}, "deviceAvailabilityQueryIntervalMs": {"type": "number"}, "sendNodeDevicesToHubIntervalMs": {"type": "number"}, "checkStaleDevicesIntervalMs": {"type": "number"}, "checkBlockedDevicesIntervalMs": {"type": "number"}, "newCommandTimeoutSec": {"type": "number"}, "bindHostOrIp": {"type": "string"}, "enableDashboard": {"type": "boolean"}, "removeDevicesFromDatabaseBeforeRunningThePlugin": {"type": "boolean"}, "bootedSimulators": {"type": "boolean"}, "remoteConnectionTimeout": {"type": "number"}, "liveStreaming": {"type": "boolean"}, "wdaBundleId": {"type": "string"}, "preBuildWDAPath": {"type": "string"}, "enableAuthentication": {"type": "boolean"}, "accessKey": {"type": "string"}, "token": {"type": "string"}, "nodeName": {"type": "string"}}, "title": "Appium device farm plugin", "type": "object"}, "scripts": {"setup": "lib/src/scripts/setup-db.js", "prepare-wda": "lib/src/scripts/ios-sign.js", "reset": "lib/src/scripts/clear-assets.js"}}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "lint-staged": {"src/*.{js,jsx,ts,tsx,json,css,scss,md}": ["npm run prettier", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "files": ["lib", "scripts", "prisma"], "typedoc": {"entryPoint": "lib/src/index.js"}}