---
title: Overview
hide:
  - navigation
  - toc
---


<div style="text-align: center">
  <img src="assets/images/DeviceFarm-Logo.jpg" class="center" width="200"/>
</div>

This is an Appium plugin designed to manage and create driver session on connected android devices and iOS Simulators.

:octicons-heart-fill-24:{ .heart } **Why Appium Device Farm?**

<div class="grid cards" markdown>

-   :status-mgt:{ .device } __Enhanced Session Management__

	---

	Streamline your testing workflow for efficiency with our solution, which simplifies the setup and oversight of driver sessions on both iOS simulators and Android devices

-   :status-automated:{ .device } __Automated Device Recognition__

	---

	Enjoy a hassle-free testing experience as our system automatically detects connected Android and iOS devices—both simulators and real hardware—prior to session initiation.

-   :status-sync:{ .device } __Proactive Device Pool Management__

	---

	During test execution, our solution updates the device pool with newly available devices, ensuring that your tests run on the latest resources.


-   :status-settings:{ .device } __Parallel Testing Enhancement__

	---

	Facilitate the concurrent running of multiple tests through the allocation of random ports, significantly boosting efficiency and decreasing total testing duration.


-   :status-gps:{ .device } __Remote Testing Facility__

	---

    Cater to the needs of geographically distributed teams or diversified testing settings with capabilities for remote device test executions.

-   :status-livetesting:{ .device } __Manual Device Interaction__

	---

	Grant testers the power for hands-on device control during tests. Whether for exploratory, debugging, or user interaction tests, our platform enhances testing depth and quality by offering complete device commands.

-   :status-remote:{ .device } __Cloud-Based Testing__
    
    ---
 	
	Integrates flawlessly with cloud services, allowing for test executions on cloud-hosted devices, broadening your testing scope.

-   :status-report:{ .device } __Advanced Reporting Insights__

	---

	Equipped with an extensive reporting dashboard, our platform delivers in-depth analyses of your testing outcomes, enabling informed decision-making and strategic planning.
</div>

Use the navigation on the left or proceed to [Setup](setup.md)!

**Big thanks to the following organizations for their support to the project with their open source licenses:**
<h3>
	<a href= "https://www.browserstack.com"><img src="https://maddyness-uk.twic.pics/2021/06/Screenshot-2021-06-21-at-20.14.46.png?twic=v1/resize=630" alt="ATD" width="45%" align="top"></a>
</h3>

<h3>
	<a href= "https://www.lambdatest.com"><img src="assets/images/lt.png" alt="ATD" width="45%" align="top"></a>
</h3>
