.md-source__fact--version {
  display: none;
}
.md-source__fact:nth-child(1n + 2):before {
  margin-left: 0 !important;
}
:root {
  --md-primary-fg-color: #9d3555;
  --md-primary-fg-color--light: #ECB7B7;
  --md-primary-fg-color--dark:  #fdcc07;
}

.md-tabs {
  color: white;
  font-weight: 900;
}

.md-typeset.a {
  color: rgb(207, 20, 20);
}

@keyframes heart {
  0%, 40%, 80%, 100% {
    transform: scale(1);
  }
  20%, 60% {
    transform: scale(1.15);
  }
}
.heart {
  animation: heart 1000ms infinite;
  color: red;
}

.device {
  font-size: 2em;
}

