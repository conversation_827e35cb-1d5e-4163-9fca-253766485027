datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Build {
  id        String    @id @default(uuid()) @map("id")
  name      String?   @map("name")
  sessions  Session[] @relation("BuildToSession")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
}

model Session {
  id                  String       @id @map("id")
  buildId             String?      @map("build_id")
  build               Build?       @relation("BuildToSession", fields: [buildId], references: [id])
  name                String?      @map("name")
  status              String       @default("running") @map("status")
  desiredCapabilities String       @map("desired_capabilities")
  sessionCapabilities String       @map("session_capabilities")
  nodeId              String       @map("node_id")
  hasLiveVideo        Boolean      @map("has_live_video")
  videoRecording      String?      @map("video_recording")
  deviceLogs          String?      @map("device_logs")
  appProfiling        String?      @map("app_profiling")
  startTime           DateTime     @default(now()) @map("start_time")
  endTime             DateTime?    @map("end_time")
  failureReason       String?      @map("failure_reason")
  deviceUdid          String       @map("device_udid")
  devicePlatform      String       @map("device_platform")
  deviceVersion       String       @map("device_version")
  deviceName          String?      @map("device_name")
  createdAt           DateTime     @default(now()) @map("created_at")
  updatedAt           DateTime     @updatedAt @map("updated_at")
  sessionLogs         SessionLog[] @relation("SessionToSessionLog")
}

model SessionLog {
  id          String   @id @default(uuid()) @map("id")
  sessionId   String   @map("session_id")
  session     Session  @relation("SessionToSessionLog", fields: [sessionId], references: [id])
  commandName String?  @map("command_name")
  url         String   @map("url")
  method      String   @map("method")
  title       String   @map("title")
  subtitle    String?  @map("subtitle")
  body        String?  @map("body")
  response    String   @map("response")
  screenshot  String?  @map("screenshot")
  isSuccess   Boolean? @map("is_success")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  eventId     String?
}

model TestEventJournal {
  id                 String    @id @default(uuid())
  session_id         String
  event_uuid         String    @unique
  event_type         String
  event_sub_type     String
  name               String
  scopes             String
  result             String?
  started_at         DateTime?
  finished_at        DateTime?
  start_event_doc    String?
  finished_event_doc String?
  file               String
}

model AppInformation {
  id               String   @id @default(uuid())
  fileName         String
  uploadedFileName String
  path             String
  platform         String
  fileSize         String   @default("0")
  appBundleId      String   @default("")
  createdAt        DateTime @default(now()) @map("created_at")
}

model DeviceTags {
  host String
  udid String
  tags String?

  @@unique([host, udid])
}

model User {
  id           String       @id @default(uuid())
  firstname    String
  lastname     String
  username     String       @unique @map("username")
  password     String       @map("password")
  role         String       @default("user") @map("role") // "admin" or "user"
  isActive     Boolean      @default(true) @map("is_active")
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")
  accessKey    String       @unique @map("access_key")
  teamMembers  TeamMember[] @relation("UserToTeamMember")
  apiTokens    ApiToken[]   @relation("UserToApiToken")
  nodes        Node[]   @relation("UserToNode")
}

model Team {
  id                String             @id @default(uuid())
  name              String             @unique @map("name")
  description       String?            @map("description")
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @updatedAt @map("updated_at")
  teamMembers       TeamMember[]       @relation("TeamToTeamMember")
  teamDevices       TeamDevice[]       @relation("TeamToTeamDevice")
}

model TeamMember {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  user      User     @relation("UserToTeamMember", fields: [userId], references: [id], onDelete: Cascade)
  teamId    String   @map("team_id")
  team      Team     @relation("TeamToTeamMember", fields: [teamId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([userId, teamId])
}

model TeamDevice {
  id        String   @id @default(uuid())
  deviceId  String   @map("device_id")
  teamId    String   @map("team_id")
  team      Team     @relation("TeamToTeamDevice", fields: [teamId], references: [id], onDelete: Cascade)
  device    Device   @relation("DeviceToTeamDevice", fields: [deviceId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([deviceId, teamId])
}

model Device {
  id              String   @id
  udid            String   @map("udid")
  host            String   @map("host")
  nodeId          String   @map("node_id")
  platform        String   @map("platform") // ios, android
  version         String   @map("version")
  name            String   @map("name")
  tags            String?  @map("tags")
  real            Boolean  @default(false) @map("real")
  isActive        Boolean  @default(true) @map("is_active")
  isFlagged        Boolean  @default(false) @map("is_flagged")
  flaggedReason   String?   @map("flagged_reason")
  usage           Int      @default(0) @map("usage")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  teamDevices     TeamDevice[] @relation("DeviceToTeamDevice")
  node            Node     @relation("NodeToDevice", fields: [nodeId], references: [id], onDelete: Cascade)
  @@unique([udid, host])
}


model ApiToken {
  id              String   @id
  name            String   @map("name")
  userId          String   @map("user_id")
  token           String
  expiresAt       DateTime? @map("expires_at")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  user            User     @relation("UserToApiToken", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, name])
}

model Node {
  id              String   @id
  name            String   @map("name")
  host            String   @map("host")
  tags            String?  @map("tags")
  os              String   @map("os")
  jwtSecretToken  String   @map("jwt_secret_token")
  isHub           Boolean  @default(false) @map("is_hub")
  isOnline        Boolean  @default(true) @map("is_online")
  addedBy         String?   @map("added_by")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  devices         Device[] @relation("NodeToDevice")

  addedByUser     User?     @relation("UserToNode", fields: [addedBy], references: [id], onDelete: Cascade)
}
