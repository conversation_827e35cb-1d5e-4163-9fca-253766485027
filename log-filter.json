[{"pattern": "\"df:jwt\":\\s*\"([^\"\n]+)\"", "flags": "i", "replacer": "**JWT**"}, {"pattern": "\"df:accesskey\":\\s*\"([^\"\n]+)\"", "flags": "i", "replacer": "**ACCESS_KEY**"}, {"pattern": "\"df:token\":\\s*\"([^\"\n]+)\"", "flags": "i", "replacer": "**PASSWORD**"}, {"pattern": "\"username\":\\s*\"([^\"\n]+)\"", "flags": "i", "replacer": "**USERNAME**"}, {"pattern": "\"password\":\\s*\"([^\"\n]+)\"", "flags": "i", "replacer": "**PASSWORD**"}, {"pattern": "\"accesskey\":\\s*\"([^\"\n]+)\"", "flags": "i", "replacer": "**ACCESS_KEY**"}, {"pattern": "\"token\":\\s*\"([^\"\n]+)\"", "flags": "i", "replacer": "**TOKEN**"}]