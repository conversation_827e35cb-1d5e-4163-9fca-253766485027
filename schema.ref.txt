Session:
------------
{
  protocol: "W3C",
  value: [
    "7ff721e7-35e0-4c9d-bd28-1a24de3be025",
    {
      platform: "LINUX",
      webStorageEnabled: false,
      takesScreenshot: true,
      javascriptEnabled: true,
      databaseEnabled: false,
      networkConnectionEnabled: true,
      locationContextEnabled: false,
      warnings: {
      },
      desired: {
        platformName: "Android",
        deviceName: "Pixel_4_31",
        platformVersion: "12",
        orientation: "PORTRAIT",
        automationName: "UiAutomator2",
        app: "/Users/<USER>/Documents/git/oss/appium-boilerplate/apps/Android-NativeDemoApp-0.4.0.apk",
        appWaitActivity: "com.wdiodemoapp.MainActivity",
        newCommandTimeout: 240,
        udid: "emulator-5554",
        systemPort: 54102,
        chromeDriverPort: 54103,
        adbPort: 5037,
        mjpegServerPort: 54100,
      },
      platformName: "Android",
      deviceName: "emulator-5554",
      platformVersion: "12",
      orientation: "PORTRAIT",
      automationName: "UiAutomator2",
      app: "/Users/<USER>/Documents/git/oss/appium-boilerplate/apps/Android-NativeDemoApp-0.4.0.apk",
      appWaitActivity: "com.wdiodemoapp.MainActivity",
      newCommandTimeout: 240,
      udid: "emulator-5554",
      systemPort: 54102,
      chromeDriverPort: 54103,
      adbPort: 5037,
      mjpegServerPort: 54100,
      deviceUDID: "emulator-5554",
      appPackage: "com.wdiodemoapp",
      deviceApiLevel: 31,
      deviceScreenSize: "1080x2280",
      deviceScreenDensity: 440,
      deviceModel: "sdk_gphone64_arm64",
      deviceManufacturer: "Google",
      pixelRatio: 2.75,
      statBarHeight: 66,
      viewportRect: {
        left: 0,
        top: 66,
        width: 1080,
        height: 2082,
      },
    },
    "W3C",
  ],
}

Device:
---------
{
  adbPort: 5037,
  systemPort: 53264,
  sdk: "12",
  realDevice: false,
  name: "sdk_gphone64_arm64",
  busy: false,
  state: "device",
  udid: "emulator-5554",
  platform: "android",
  deviceType: "emulator",
  host: "http://*************:4723",
  totalUtilizationTimeMilliSec: 30799,
  sessionStartTime: 0,
  userBlocked: false,
  nodeId: "aa920192-dea4-402f-8adf-d517c715a684",
  offline: false,
  meta: {
    revision: 6,
    created: 1696916346503,
    version: 0,
    updated: 1696916407141,
  },
  $loki: 2,
  newCommandTimeout: undefined,
  session_id: undefined,
  lastCmdExecutedAt: undefined,
}