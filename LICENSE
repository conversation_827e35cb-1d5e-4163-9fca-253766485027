Appium Device Farm Plugin License

The Appium Device Farm Plugin is dual-licensed:

1. Open Source Components License (MIT License)

Copyright (c) 2024 Appium Test Distribution aka Appium Device Farm

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

This license applies to all files and directories in this project except those
listed under "Proprietary Components" below.

2. Proprietary Components

The following components are proprietary and subject to the Appium Device Farm
Proprietary License:

- src/modules/dashboard
- src/modules/device-control
- dashboard-frontend

For the full text of the proprietary license, see the accompanying
PROPRIETARY-LICENSE.txt file.
