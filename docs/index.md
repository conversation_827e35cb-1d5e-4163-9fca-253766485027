---
title: Overview
hide:
  - navigation
---

<div style="text-align: center">
  <img src="assets/images/DeviceFarm-Logo.jpg" class="center" />
</div>

This is an Appium plugin designed to manage and create driver session on connected android, iOS real devices, emulators and Simulators.

**Why Appium Device Farm?**

- Automatically detects connected Android, iOS Simulators and Real devices before session creation and maintains it in device pool.
- Dynamically allocates a free device from device pool while creating driver session.
- Updates the device pool with a new device during test execution.
- Allocates random ports for parallel execution.
- Remote execution
- Async lockup for device changes.

Use the navigation on the left or proceed to [Setup](setup.md)!

**Big thanks to the following organizations for their support to the project with their open source licenses:**
<h3>
	<a href= "https://www.browserstack.com"><img src="https://maddyness-uk.twic.pics/2021/06/Screenshot-2021-06-21-at-20.14.46.png?twic=v1/resize=630" alt="ATD" width="45%" align="top"></a>
</h3>

<h3>
	<a href= "https://www.lambdatest.com"><img src="assets/images/lt.png" alt="ATD" width="45%" align="top"></a>
</h3>
