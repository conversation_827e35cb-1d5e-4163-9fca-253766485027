---
title: Capabilities
---

| Capability Name              | Description                                                                                                                                                                                                                                                        |
| ---------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| df:build                     | Allocate a build name in dashboard. Default value is `null`.                                                                                                                                                                                                       |
| df:liveVideo                 | Enable live video for the session. Default value is `true`.                                                                                                                                                                                                        |
| df:recordVideo               | Enable video recording for the session. Default value is `false`.                                                                                                                                                                                                  |
| df:videoTimeLimit            | Maximum duration of the video recording in seconds. Default value is 1800 seconds (3 minutes).                                                                                                                                                                     |
| df:iPhoneOnly                | Allocate only iPhone simulators for execution when to true. Default value is `false`.                                                                                                                                                                              |
| df:iPadOnly                  | Allocate only iPad simulators for execution when to true. Default value is `false`.                                                                                                                                                                                |
| df:deviceAvailabilityTimeout | When create session requests are more than available connected devices, plugin waits for a certain interval for device availability before it timeout. Default value is `180000` milliseconds.                                                                     |
| df:deviceRetryInterval       | When create session requests are more than available connected devices, plugin polls for device availability in certain intervals. Default value is `10000` milliseconds.                                                                                          |
| df:udids                     | Comma separated list of device udid's to execute tests only on specific devices `df:udids: device1UDID,device2UDID`                                                                                                                                                |
| appium:platformName          | Requests asession for the provided platform name. Valid options are `iOS`, `tvOS`, or `Android`, ex: `'appium:platformName': tvOS`                                                                                                                                 |
| appium:platformVersion       | This capability is used to filter devices/simulators based on SDK. Only devices/simulators that are an exact match with the platformVerson would be considered for test run. `appium:platformVersion` is optional argument. ex: `'appium:platformVersion': 16.1.1` |
| df:minSDK                    | This capability is used to filter devices/simulators based on SDK. Devices/Simulators with SDK greater then or equal to minSDK would only be considered for test run. `df:minSDK` is optional argument. ex: `'appium:minSDK': 15`                                  |
| df:maxSDK                    | This capability is used to filter devices/simulators based on SDK. Devices/Simulators with SDK less then or equal to maxSDK would only be considered for test run. `df:maxSDK` is optional argument. ex: `'appium:maxSDK': 15`                                     |
| df:filterByHost              | This capability is used to filter devices/simulators based on node IP. This will only consider devices from specific node. `df:options` is optional argument. ex: `'filterByHost': '*************',`                                                               |
| df:options                   | Set all device farm related capabilities as object. `df:options` is optional argument. ex: `'df:options': { filterByHost: '*************', recordVideo: true },`                                                                                                   |
| df:saveDeviceLogs            | Save device logs, app profiling for android. `df:options` is optional argument. ex: `'df:options': { saveDeviceLogs: true },`. Default value is `false`                                                                                                            |
| df:tags                      | Will consider the devices only with the tagged specified. `df:tags` is optional argument. ex: `'df:tags': ['team1','AndroidGroup'],`. Default value is empty array.                                                                                                |
