export const deviceMock = [
  {
    busy: true,
    state: 'device',
    udid: 'emulator-5570',
    platform: 'android',
    offline: false,
    host: '127.0.0.1',
  },
  {
    busy: false,
    state: 'device',
    udid: 'emulator-5554',
    platform: 'android',
    offline: false,
    host: '127.0.0.1',
  },
  {
    busy: false,
    state: 'device',
    udid: 'emulator-5556',
    platform: 'android',
    offline: true,
    host: '127.0.0.1',
  },
  {
    name: 'iPad Air',
    udid: '0FBCBDCC-2FF1-4FCA-B034-60ABC86ED888',
    state: 'Booted',
    deviceType: 'simulator',
    sdk: '13.5',
    platform: 'ios',
    busy: false,
    realDevice: false,
    offline: false,
  },
  {
    name: 'iPad Air (3rd generation)',
    udid: '0FBCBDCC-2FF1-4FCA-B034-60ABC86ED866',
    state: 'Shutdown',
    deviceType: 'simulator',
    sdk: '13.5',
    platform: 'ios',
    busy: false,
    realDevice: false,
    offline: false,
  },
  {
    name: 'iPhone SE (3rd generation)',
    udid: 'DE906983-2AE2-4413-94A0-FB519077DDC7',
    state: 'Shutdown',
    sdk: '16.1',
    platform: 'iOS',
  },
  {
    name: 'iPhone 14',
    udid: '07C2777D-E8D9-440B-8C93-137B4F2400C6',
    state: 'Shutdown',
    sdk: '16.1',
    platform: 'iOS',
  },
  {
    name: 'iPhone 14 Plus',
    udid: '4AA875AB-3A80-47B4-91E4-F16854171C38',
    state: 'Shutdown',
    sdk: '16.1',
    platform: 'iOS',
  },
  {
    name: 'iPhone 14 Pro',
    udid: 'A9FDE5C7-3BF9-41D8-8F80-140109E1F43A',
    state: 'Shutdown',
    sdk: '16.1',
    platform: 'iOS',
  },
];
